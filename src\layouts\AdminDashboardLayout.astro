---
import '../styles/global.css';
import NetlifyIdentityHelper from '../components/NetlifyIdentityHelper.astro';

interface Props {
  title?: string;
}
const { title = 'USC Perchtoldsdorf Admin' } = Astro.props;

// Custom CSP for dashboard to allow Google Analytics
const customCSP =
  "default-src 'self' https://app.netlify.com/; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://app.netlify.com/ https://identity.netlify.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; connect-src 'self' https://app.netlify.com/ https://identity.netlify.com https://www.google-analytics.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com; frame-src 'self' https://app.netlify.com/;";
---

<!doctype html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title}</title>
    <slot name="head" />
    <NetlifyIdentityHelper />
    <script is:inline src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>

    <!-- Apply custom CSP to allow Google Analytics -->
    <meta http-equiv="Content-Security-Policy" content={customCSP} slot="head" />
  </head>
  <body class="min-h-screen flex flex-col bg-gray-50">
    <header class="bg-usc-primary text-white shadow-md">
      {/* Desktop Header */}
      <div class="container mx-auto px-4 py-3 border-b items-center shadow-bottom hidden md:flex justify-between">
        <div class="flex items-center gap-4">
          <a href="/admin/dashboard" class="flex items-center gap-2 font-bold">
            <div class="logo-icon rounded-full p-1">
              <img
                src="/uploads/images/static/logo.png"
                alt="Vereinswappen/Logo des USC-Perchtoldsdorf"
                class="w-12 h-auto"
              />
            </div>
            USC Admin
          </a>
        </div>
        <nav>
          <ul class="flex flex-wrap gap-4 text-sm">
            <li><a href="/admin/media" class="hover:underline">Medien</a></li>
            <li><a href="/admin/cms/" class="hover:underline">CMS</a></li>
            <li><a href="/admin/docs" class="hover:underline">Dokumentation</a></li>
            <li><a href="/admin/settings" class="hover:underline">Einstellungen</a></li>
            <li><a href="#" id="logout" class="hover:underline">Logout</a></li>
          </ul>
        </nav>
      </div>
      <div class="container mx-auto px-4 py-2 hidden md:flex justify-between items-center">
        <ul class="flex flex-wrap gap-4 text-sm">
          <li><a href="/admin/dashboard" class="hover:underline">Dashboard</a></li>
          <li><a href="/admin/pages" class="hover:underline">Seiten</a></li>
          <li><a href="/admin/news" class="hover:underline">News</a></li>
          <li><a href="/admin/teams" class="hover:underline">Mannschaften</a></li>
          <li><a href="/admin/products" class="hover:underline">Produkte</a></li>
          <li><a href="/admin/sponsors" class="hover:underline">Sponsoren</a></li>
          <li><a href="/admin/countdowns" class="hover:underline">Countdowns</a></li>
          <li><a href="/admin/events" class="hover:underline">Events</a></li>
          <li><a href="/admin/blobs" class="hover:underline">Schedules</a></li>
        </ul>
        <a href="/" class="text-sm hover:underline whitespace-nowrap">Zurück zur Website</a>
      </div>

      {/* Mobile Header */}
      <div class="md:hidden">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
          <a href="/admin/dashboard" class="flex items-center gap-2 font-bold">
            <div class="logo-icon rounded-full p-1">
              <img
                src="/uploads/images/static/logo.png"
                alt="Vereinswappen/Logo des USC-Perchtoldsdorf"
                class="w-8 h-auto"
              />
            </div>
            <span class="text-sm">USC Admin</span>
          </a>
          <button id="mobileMenuBtn" class="p-2" aria-label="Toggle Menu">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
        
        <div id="mobileMenu" class="hidden px-4 py-2 border-t border-usc-primary-dark">
          <nav class="space-y-4">
            <div class="space-y-2">
              <a href="/admin/media" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Medien</a>
              <a href="/admin/cms/" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">CMS</a>
              <a href="/admin/settings" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Einstellungen</a>
              <a href="/admin/docs" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Dokumentation</a>
            </div>
            <div class="space-y-2 pt-2 border-t border-usc-primary-dark">
              <a href="/admin/dashboard" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Dashboard</a>
              <a href="/admin/pages" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Seiten</a>
              <a href="/admin/news" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">News</a>
              <a href="/admin/teams" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Mannschaften</a>
              <a href="/admin/products" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Produkte</a>
              <a href="/admin/sponsors" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Sponsoren</a>
              <a href="/admin/countdowns" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Countdowns</a>
              <a href="/admin/events" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Events</a>
              <a href="/admin/blobs" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Schedules</a>
            </div>
            <div class="pt-2 border-t border-usc-primary-dark">
              <a href="/" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Zurück zur Website</a>
              <a href="#" id="logoutMobile" class="block hover:bg-usc-primary-dark px-2 py-1 rounded">Logout</a>
            </div>
          </nav>
        </div>
      </div>

      <script>
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        
        if (mobileMenuBtn && mobileMenu) {
          mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
          });
        }
      </script>
    </header>
    <main class="flex-1">
      <slot />
    </main>
    <script is:inline>
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on('init', user => {
          if (user) {
            // User is logged in, continue showing dashboard
            const btn = document.getElementById('logout');
            if (btn) {
              btn.addEventListener('click', e => {
                e.preventDefault();
                window.netlifyIdentity.logout();
                window.location.href = '/admin';
              });
            }
          } else {
            console.error('No User Found. Redirecting...');
            // User is not logged in, redirect to admin login
            window.location.href = '/admin';
          }

          // Listen for logout event
          window.netlifyIdentity.on('logout', () => {
            console.log('Logging out. Redirecting...');
            window.location.href = '/admin';
          });
        });
      } else {
        console.error('Coundnt load Netlify Identity Widget');
        // Netlify Identity not loaded, redirect to admin
        window.location.href = '/admin';
      }
    </script>
  </body>
</html>
